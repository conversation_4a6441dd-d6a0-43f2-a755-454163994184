using UnityEngine;

/// <summary>
/// Advanced RTS Camera Controller inspired by WARNO's camera system
/// Features: Edge scrolling, WASD movement, mouse drag, zoom, rotation, height adjustment
/// Perfect for hyper-realistic wargaming simulations with smooth interpolation and bounds
/// </summary>
public class RTSCamera : MonoBehaviour
{
    [Header("Movement Settings")]
    [SerializeField] private float panSpeed = 20f;
    [SerializeField] private float edgeScrollSpeed = 15f;
    [SerializeField] private float mouseDragSpeed = 2f;
    [SerializeField] private int edgeScrollBorder = 10;
    [SerializeField] private bool enableEdgeScrolling = true;
    [SerializeField] private float movementSmoothTime = 0.1f;

    [Header("Zoom Settings")]
    [SerializeField] private float zoomSpeed = 10f;
    [SerializeField] private float minZoom = 5f;
    [SerializeField] private float maxZoom = 50f;
    [SerializeField] private float zoomSmoothTime = 0.3f;
    [SerializeField] private bool zoomT<PERSON>rdsMouse = true;

    [Header("Rotation Settings")]
    [SerializeField] private float rotationSpeed = 100f;
    [SerializeField] private bool enableRotation = true;
    [SerializeField] private float rotationSmoothTime = 0.2f;
    [SerializeField] private bool snapToCardinalDirections = false;
    [SerializeField] private float snapAngle = 45f;

    [Header("Height Settings")]
    [SerializeField] private float minHeight = 5f;
    [SerializeField] private float maxHeight = 100f;
    [SerializeField] private float heightSmoothTime = 0.3f;
    [SerializeField] private float heightSpeed = 20f;

    [Header("Bounds")]
    [SerializeField] private bool useBounds = true;
    [SerializeField] private Vector2 minBounds = new Vector2(-100, -100);
    [SerializeField] private Vector2 maxBounds = new Vector2(100, 100);

    [Header("Advanced Features")]
    [SerializeField] private float fastMoveMultiplier = 2f;
    [SerializeField] private bool enableInertia = true;
    [SerializeField] private float inertiaDamping = 5f;
    [SerializeField] private bool enableShake = false;
    [SerializeField] private float shakeIntensity = 0.1f;
    
    // Components
    private Camera cam;
    private RTSCameraInputManager inputManager;

    // Movement variables
    private Vector3 currentVelocity;
    private Vector3 targetPosition;
    private Vector3 positionVelocity;

    // Zoom variables
    private float currentZoom;
    private float targetZoom;
    private float zoomVelocity;

    // Rotation variables
    private float currentRotation;
    private float targetRotation;
    private float rotationVelocity;

    // Height variables
    private float currentHeight;
    private float targetHeight;
    private float heightVelocity;

    // Shake variables
    private Vector3 shakeOffset;
    private float shakeTimer;

    // State tracking
    private bool wasRotating;
    private Vector3 lastMouseWorldPosition;
    
    void Start()
    {
        cam = GetComponent<Camera>();
        if (cam == null)
        {
            Debug.LogError("RTSCamera: No Camera component found!");
            enabled = false;
            return;
        }

        // Get or create input manager
        inputManager = GetComponent<RTSCameraInputManager>();
        if (inputManager == null)
        {
            inputManager = gameObject.AddComponent<RTSCameraInputManager>();
        }

        // Initialize values
        currentZoom = targetZoom = cam.fieldOfView;
        currentHeight = targetHeight = transform.position.y;
        currentRotation = targetRotation = transform.eulerAngles.y;
        targetPosition = transform.position;

        // Lock cursor to game window for better edge scrolling
        Cursor.lockState = CursorLockMode.Confined;
    }
    
    void Update()
    {
        HandleMovement();
        HandleZoom();
        HandleRotation();
        HandleHeight();
        HandleShake();
        ApplyBounds();
        ApplySmoothing();
    }

    void HandleMovement()
    {
        Vector3 movement = Vector3.zero;

        // Get movement input from input manager
        Vector3 keyboardMovement = inputManager.MovementInput;

        // Add edge scrolling
        if (enableEdgeScrolling)
        {
            Vector3 edgeMovement = GetEdgeScrollMovement();
            keyboardMovement += edgeMovement;
        }

        // Transform movement relative to camera rotation
        if (keyboardMovement != Vector3.zero)
        {
            Vector3 forward = transform.forward;
            Vector3 right = transform.right;

            // Flatten vectors to prevent unwanted vertical movement
            forward.y = 0;
            right.y = 0;
            forward.Normalize();
            right.Normalize();

            movement = (forward * keyboardMovement.z + right * keyboardMovement.x);

            float speed = panSpeed;
            if (IsMouseAtEdge()) speed = edgeScrollSpeed;
            if (inputManager.IsFastMove) speed *= fastMoveMultiplier;

            movement *= speed;
        }

        // Handle mouse drag movement
        if (inputManager.IsDragging && !inputManager.IsRotating)
        {
            Vector2 mouseDelta = inputManager.MouseDelta;
            Vector3 worldDelta = cam.ScreenToWorldPoint(new Vector3(mouseDelta.x, mouseDelta.y, cam.nearClipPlane));
            movement -= new Vector3(worldDelta.x, 0, worldDelta.z) * mouseDragSpeed;
        }

        // Apply inertia if enabled
        if (enableInertia)
        {
            currentVelocity = Vector3.Lerp(currentVelocity, movement, inertiaDamping * Time.deltaTime);
            targetPosition += currentVelocity * Time.deltaTime;
        }
        else
        {
            targetPosition += movement * Time.deltaTime;
        }
    }
    
    Vector3 GetEdgeScrollMovement()
    {
        Vector3 edgeMovement = Vector3.zero;
        Vector3 mousePos = Input.mousePosition;

        if (mousePos.x <= edgeScrollBorder) edgeMovement += Vector3.left;
        if (mousePos.x >= Screen.width - edgeScrollBorder) edgeMovement += Vector3.right;
        if (mousePos.y <= edgeScrollBorder) edgeMovement += Vector3.back;
        if (mousePos.y >= Screen.height - edgeScrollBorder) edgeMovement += Vector3.forward;

        return edgeMovement;
    }

    void HandleZoom()
    {
        float scrollInput = inputManager.ScrollInput;

        if (Mathf.Abs(scrollInput) > 0.01f)
        {
            // Store mouse position for zoom towards mouse
            Vector3 mouseWorldPos = Vector3.zero;
            if (zoomTowardsMouse)
            {
                Ray ray = cam.ScreenPointToRay(Input.mousePosition);
                if (Physics.Raycast(ray, out RaycastHit hit))
                {
                    mouseWorldPos = hit.point;
                }
            }

            float oldZoom = targetZoom;
            targetZoom -= scrollInput * zoomSpeed;
            targetZoom = Mathf.Clamp(targetZoom, minZoom, maxZoom);

            // Zoom towards mouse position
            if (zoomTowardsMouse && mouseWorldPos != Vector3.zero)
            {
                float zoomFactor = (oldZoom - targetZoom) / oldZoom;
                Vector3 direction = (mouseWorldPos - transform.position);
                direction.y = 0; // Keep on same height level
                targetPosition += direction * zoomFactor * 0.1f;
            }
        }
    }

    void HandleRotation()
    {
        if (enableRotation && inputManager.IsRotating)
        {
            float mouseX = Input.GetAxis("Mouse X");
            targetRotation += mouseX * rotationSpeed * Time.deltaTime;

            // Snap to cardinal directions if enabled
            if (snapToCardinalDirections && !wasRotating && inputManager.IsRotating)
            {
                float snappedRotation = Mathf.Round(targetRotation / snapAngle) * snapAngle;
                if (Mathf.Abs(targetRotation - snappedRotation) < snapAngle * 0.5f)
                {
                    targetRotation = snappedRotation;
                }
            }
        }

        wasRotating = inputManager.IsRotating;
    }

    void HandleHeight()
    {
        float heightInput = inputManager.HeightInput;

        if (Mathf.Abs(heightInput) > 0.01f)
        {
            targetHeight += heightInput * heightSpeed * Time.deltaTime;
            targetHeight = Mathf.Clamp(targetHeight, minHeight, maxHeight);
        }
    }

    void HandleShake()
    {
        if (!enableShake) return;

        if (shakeTimer > 0)
        {
            shakeTimer -= Time.deltaTime;
            shakeOffset = Random.insideUnitSphere * shakeIntensity * (shakeTimer / 1f);
            shakeOffset.y *= 0.5f; // Reduce vertical shake
        }
        else
        {
            shakeOffset = Vector3.Lerp(shakeOffset, Vector3.zero, Time.deltaTime * 5f);
        }
    }
    
    void ApplyBounds()
    {
        if (!useBounds) return;

        targetPosition.x = Mathf.Clamp(targetPosition.x, minBounds.x, maxBounds.x);
        targetPosition.z = Mathf.Clamp(targetPosition.z, minBounds.y, maxBounds.y);
    }

    void ApplySmoothing()
    {
        // Smooth position
        Vector3 smoothedPosition = Vector3.SmoothDamp(transform.position, targetPosition, ref positionVelocity, movementSmoothTime);
        smoothedPosition.y = Mathf.SmoothDamp(transform.position.y, targetHeight, ref heightVelocity, heightSmoothTime);

        // Apply shake offset
        smoothedPosition += shakeOffset;

        transform.position = smoothedPosition;

        // Smooth rotation
        currentRotation = Mathf.SmoothDampAngle(currentRotation, targetRotation, ref rotationVelocity, rotationSmoothTime);
        transform.rotation = Quaternion.Euler(transform.rotation.eulerAngles.x, currentRotation, 0);

        // Smooth zoom
        currentZoom = Mathf.SmoothDamp(currentZoom, targetZoom, ref zoomVelocity, zoomSmoothTime);
        cam.fieldOfView = currentZoom;
    }

    bool IsMouseAtEdge()
    {
        Vector3 mousePos = Input.mousePosition;
        return mousePos.x <= edgeScrollBorder ||
               mousePos.x >= Screen.width - edgeScrollBorder ||
               mousePos.y <= edgeScrollBorder ||
               mousePos.y >= Screen.height - edgeScrollBorder;
    }
    
    // Public methods for external control
    public void SetPosition(Vector3 position)
    {
        targetPosition = new Vector3(position.x, targetPosition.y, position.z);
    }

    public void SetZoom(float zoom)
    {
        targetZoom = Mathf.Clamp(zoom, minZoom, maxZoom);
    }

    public void SetHeight(float height)
    {
        targetHeight = Mathf.Clamp(height, minHeight, maxHeight);
    }

    public void SetRotation(float rotation)
    {
        targetRotation = rotation;
    }

    public void FocusOnPosition(Vector3 position, float zoom = -1, float height = -1, float rotation = -1)
    {
        SetPosition(position);
        if (zoom > 0) SetZoom(zoom);
        if (height > 0) SetHeight(height);
        if (rotation >= 0) SetRotation(rotation);
    }

    public void TriggerShake(float duration = 1f, float intensity = -1)
    {
        if (!enableShake) return;

        shakeTimer = duration;
        if (intensity > 0) shakeIntensity = intensity;
    }

    public void SetBounds(Vector2 min, Vector2 max)
    {
        minBounds = min;
        maxBounds = max;
    }

    public void ResetCamera()
    {
        targetPosition = Vector3.zero;
        targetRotation = 0f;
        targetZoom = (minZoom + maxZoom) * 0.5f;
        targetHeight = (minHeight + maxHeight) * 0.5f;
        currentVelocity = Vector3.zero;
        shakeTimer = 0f;
    }

    // Getters for current state
    public Vector3 GetCurrentPosition() => transform.position;
    public float GetCurrentZoom() => currentZoom;
    public float GetCurrentHeight() => currentHeight;
    public float GetCurrentRotation() => currentRotation;
    public bool IsMoving() => currentVelocity.magnitude > 0.1f || inputManager.HasMovementInput();
}
