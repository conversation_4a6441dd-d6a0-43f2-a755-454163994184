using UnityEngine;

/// <summary>
/// Input manager for RTS Camera with customizable key bindings
/// Separates input handling from camera logic for better modularity
/// </summary>
[System.Serializable]
public class RTSCameraInputSettings
{
    [Header("Movement Keys")]
    public KeyCode moveForward = KeyCode.W;
    public KeyCode moveBackward = KeyCode.S;
    public KeyCode moveLeft = KeyCode.A;
    public KeyCode moveRight = KeyCode.D;
    
    [Header("Height Keys")]
    public KeyCode moveUp = KeyCode.E;
    public KeyCode moveDown = KeyCode.Q;
    
    [Header("Modifier Keys")]
    public KeyCode fastMoveKey = KeyCode.LeftShift;
    public KeyCode rotationKey = KeyCode.Mouse2;
    public KeyCode dragKey = KeyCode.Mouse2;
    
    [Header("Alternative Controls")]
    public bool useArrowKeys = true;
    public bool useNumpadForHeight = false;
    public KeyCode alternateRotationKey = KeyCode.R;
}

public class RTSCameraInputManager : MonoBehaviour
{
    [SerializeField] private RTSCameraInputSettings inputSettings = new RTSCameraInputSettings();
    
    // Input state
    public Vector3 MovementInput { get; private set; }
    public float ScrollInput { get; private set; }
    public bool IsRotating { get; private set; }
    public bool IsDragging { get; private set; }
    public bool IsFastMove { get; private set; }
    public float HeightInput { get; private set; }
    public Vector2 MouseDelta { get; private set; }
    
    private Vector3 lastMousePosition;
    private bool wasDragging;
    
    void Update()
    {
        UpdateMovementInput();
        UpdateScrollInput();
        UpdateRotationInput();
        UpdateDragInput();
        UpdateModifierInput();
        UpdateHeightInput();
        UpdateMouseDelta();
    }
    
    void UpdateMovementInput()
    {
        MovementInput = Vector3.zero;
        
        // Primary WASD controls
        if (Input.GetKey(inputSettings.moveForward)) MovementInput += Vector3.forward;
        if (Input.GetKey(inputSettings.moveBackward)) MovementInput += Vector3.back;
        if (Input.GetKey(inputSettings.moveLeft)) MovementInput += Vector3.left;
        if (Input.GetKey(inputSettings.moveRight)) MovementInput += Vector3.right;
        
        // Alternative arrow key controls
        if (inputSettings.useArrowKeys)
        {
            if (Input.GetKey(KeyCode.UpArrow)) MovementInput += Vector3.forward;
            if (Input.GetKey(KeyCode.DownArrow)) MovementInput += Vector3.back;
            if (Input.GetKey(KeyCode.LeftArrow)) MovementInput += Vector3.left;
            if (Input.GetKey(KeyCode.RightArrow)) MovementInput += Vector3.right;
        }
        
        // Normalize diagonal movement
        if (MovementInput.magnitude > 1f)
            MovementInput = MovementInput.normalized;
    }
    
    void UpdateScrollInput()
    {
        ScrollInput = Input.GetAxis("Mouse ScrollWheel");
    }
    
    void UpdateRotationInput()
    {
        IsRotating = Input.GetKey(inputSettings.rotationKey) || 
                    (Input.GetKey(inputSettings.alternateRotationKey) && Input.GetKey(KeyCode.LeftControl));
    }
    
    void UpdateDragInput()
    {
        bool dragKeyPressed = Input.GetKey(inputSettings.dragKey);
        
        if (Input.GetMouseButtonDown(2) || (dragKeyPressed && !wasDragging))
        {
            lastMousePosition = Input.mousePosition;
            IsDragging = true;
        }
        else if (Input.GetMouseButtonUp(2) || (!dragKeyPressed && wasDragging))
        {
            IsDragging = false;
        }
        
        wasDragging = dragKeyPressed;
    }
    
    void UpdateModifierInput()
    {
        IsFastMove = Input.GetKey(inputSettings.fastMoveKey);
    }
    
    void UpdateHeightInput()
    {
        HeightInput = 0f;
        
        if (Input.GetKey(inputSettings.moveUp)) HeightInput += 1f;
        if (Input.GetKey(inputSettings.moveDown)) HeightInput -= 1f;
        
        // Numpad alternative for height
        if (inputSettings.useNumpadForHeight)
        {
            if (Input.GetKey(KeyCode.KeypadPlus)) HeightInput += 1f;
            if (Input.GetKey(KeyCode.KeypadMinus)) HeightInput -= 1f;
        }
    }
    
    void UpdateMouseDelta()
    {
        if (IsDragging)
        {
            Vector3 currentMousePosition = Input.mousePosition;
            MouseDelta = currentMousePosition - lastMousePosition;
            lastMousePosition = currentMousePosition;
        }
        else
        {
            MouseDelta = Vector2.zero;
        }
    }
    
    // Public methods for external configuration
    public void SetInputSettings(RTSCameraInputSettings newSettings)
    {
        inputSettings = newSettings;
    }
    
    public RTSCameraInputSettings GetInputSettings()
    {
        return inputSettings;
    }
    
    // Method to check if any movement input is active
    public bool HasMovementInput()
    {
        return MovementInput != Vector3.zero || IsDragging;
    }
    
    // Method to get combined movement vector including drag
    public Vector3 GetTotalMovementInput(Camera cam, float dragSensitivity = 1f)
    {
        Vector3 totalMovement = MovementInput;
        
        if (IsDragging && MouseDelta != Vector2.zero)
        {
            // Convert screen space drag to world space movement
            Vector3 worldDelta = cam.ScreenToWorldPoint(new Vector3(MouseDelta.x, MouseDelta.y, cam.nearClipPlane));
            Vector3 dragMovement = new Vector3(-worldDelta.x, 0, -worldDelta.z) * dragSensitivity;
            totalMovement += dragMovement;
        }
        
        return totalMovement;
    }
}
